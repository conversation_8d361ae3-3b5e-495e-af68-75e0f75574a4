<?php

namespace App\Builders\Billing\Report;

use App\Enums\Billing\InvoiceReportsGrouping;
use App\Enums\Billing\InvoiceStates;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceSnapshot;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class AgedInvoicesBuilder extends BaseInvoiceReportBuilder
{
    protected array $sortColumnsMap = [
        'company_id'   => Invoice::TABLE . '.' . Invoice::FIELD_COMPANY_ID,
        'invoice_id'   => Invoice::TABLE . '.' . Invoice::FIELD_ID,
        '0_15'         => '0_15',
        '16_30'        => '16_30',
        '31_60'        => '31_60',
        '61_90'        => '61_90',
        '90_plus'      => '90_plus',
        'total_issued' => 'total_issued',
    ];

    /**
     * @param Builder $query
     */
    private function __construct(Builder $query)
    {
        parent::__construct($query);
    }

    /**
     * @return self
     */
    public static function query(): AgedInvoicesBuilder
    {
        $query = InvoiceSnapshot::mostRecentByInvoice()
            ->select([
                    DB::raw('SUM(CASE WHEN DATEDIFF(NOW(), ' . Invoice::TABLE . '.' . Invoice::FIELD_ISSUE_AT . ') BETWEEN 0 AND 15 THEN invoice_snapshots.total_outstanding ELSE null END) AS `0_15`',),
                    DB::raw('SUM(CASE WHEN DATEDIFF(NOW(), ' . Invoice::TABLE . '.' . Invoice::FIELD_ISSUE_AT . ') BETWEEN 16 AND 30 THEN invoice_snapshots.total_outstanding ELSE null END) AS `16_30`',),
                    DB::raw('SUM(CASE WHEN DATEDIFF(NOW(), ' . Invoice::TABLE . '.' . Invoice::FIELD_ISSUE_AT . ') BETWEEN 31 AND 60 THEN invoice_snapshots.total_outstanding ELSE null END) AS `31_60`',),
                    DB::raw('SUM(CASE WHEN DATEDIFF(NOW(), ' . Invoice::TABLE . '.' . Invoice::FIELD_ISSUE_AT . ') BETWEEN 61 AND 90 THEN invoice_snapshots.total_outstanding ELSE null END) AS `61_90`',),
                    DB::raw('SUM(CASE WHEN DATEDIFF(NOW(), ' . Invoice::TABLE . '.' . Invoice::FIELD_ISSUE_AT . ') > 90 THEN invoice_snapshots.total_outstanding ELSE null END) AS `90_plus`'),
                    DB::raw('SUM(' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_OUTSTANDING . ') as total_issued'),
                ]
            )
            ->join(Invoice::TABLE, Invoice::TABLE . '.' . Invoice::FIELD_ID, InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_INVOICE_ID)
            ->where(InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_OUTSTANDING, '>', 0)
            ->whereDoesntHave(InvoiceSnapshot::RELATION_INVOICE, function (Builder $query) {
                $query->whereIn(Invoice::FIELD_STATUS, [
                    InvoiceStates::DRAFT->value,
                    InvoiceStates::VOIDED->value,
                    InvoiceStates::DELETED->value,
                    InvoiceStates::WRITTEN_OFF->value
                ]);
            });

        return new self($query);
    }
}
