<?php

namespace App\Repositories;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryLog;
use App\Models\Odin\ConsumerProduct;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class CompanyCampaignDeliveryLogRepository
{
    /**
     * @param  int  $perPage
     * @param  int  $page
     * @param  int|null  $companyId
     * @param  bool|null  $succeeded
     * @param  string|null  $campaign
     * @param  int|null  $consumerProductId
     * @param  array|null  $dateRange
     *
     * @return LengthAwarePaginator
     */
    public function listDeliveryLogs(
        int     $perPage,
        int     $page,
        ?int    $companyId = null,
        ?bool   $succeeded = null,
        ?string $campaign = null,
        ?int    $consumerProductId = null,
        ?array  $dateRange = null
    ): LengthAwarePaginator
    {
        return $this->baseQuery(companyId: $companyId,succeeded: $succeeded,campaign: $campaign,consumerProductId: $consumerProductId, dateRange: $dateRange)->paginate($perPage, ['*'], 'page', $page);
    }

    /**
     * @param int $limit
     * @param int|null $companyId
     * @param bool|null $succeeded
     * @param string|null $campaign
     * @param int|null $consumerProductId
     * @return Collection
     */
    public function listAllDeliveryLogs(int $limit= 500, ?int $companyId = null, ?bool $succeeded = null, ?string $campaign = null, ?int $consumerProductId = null): Collection
    {
        return $this->baseQuery(companyId: $companyId,succeeded: $succeeded,campaign: $campaign,consumerProductId: $consumerProductId, dateRange: null)->limit($limit)->get();
    }

    /**
     * @param  int|null  $companyId
     * @param  bool|null  $succeeded
     * @param  string|null  $campaign
     * @param  int|null  $consumerProductId
     * @param  array|null  $dateRange
     *
     * @return Builder
     */
    protected function baseQuery(?int $companyId, ?bool $succeeded, ?string $campaign, ?int $consumerProductId, ?array $dateRange): Builder
    {
        $query = CompanyCampaignDeliveryLog::query();

        if (isset($companyId)) {
            $query->whereHas(CompanyCampaignDeliveryLog::RELATION_CAMPAIGN, function (Builder $builder) use ($companyId) {
                $builder->withTrashed()->where(CompanyCampaign::FIELD_COMPANY_ID, $companyId);
            });
        }

        if (isset($succeeded)) {
            $query->where(CompanyCampaignDeliveryLog::FIELD_SUCCESS, $succeeded);
        }

        if (isset($campaign)) {
            $query->whereHas(CompanyCampaignDeliveryLog::RELATION_CAMPAIGN, function (Builder $builder) use ($campaign) {
                $builder->withTrashed()->whereAny([CompanyCampaign::FIELD_ID, CompanyCampaign::FIELD_NAME],'LIKE', '%' . $campaign . '%');
            });
        }

        if (isset($consumerProductId)) {
            $query->whereHas(CompanyCampaignDeliveryLog::RELATION_PRODUCT, function (Builder $builder) use ($consumerProductId) {
               $builder->where(ConsumerProduct::FIELD_ID, $consumerProductId);
            });
        }

        if (isset($dateRange)) {
            $query->whereBetween(CompanyCampaignDeliveryLog::FIELD_CREATED_AT, [$dateRange['from'],  $dateRange['to']]);
        }

        return $query->orderByDesc(CompanyCampaignDeliveryLog::FIELD_CREATED_AT);
    }
}
