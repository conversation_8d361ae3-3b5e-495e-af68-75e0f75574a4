<?php

namespace App\Services\QAAutomation;

use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use App\Models\QAAutomation\QAAutomationIndustryService;
use App\Models\QAAutomation\QAAutomationLog;
use App\Services\QAAutomation\AutomationServices\RegexQAAutomation;

class QAAutomationService
{
    /**
     * @param RegexQAAutomation $regexService
     */
    public function __construct(
        protected RegexQAAutomation $regexService,
    ) {}

    /**
     * Main Entry for Automated QA Process for Leads
     * Returns true if automation is configured, enabled, and lead passes checks - false otherwise
     * @param int $consumerProductId
     * @return bool
     */
    public function qualifyConsumerProduct(int $consumerProductId): bool
    {
        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = ConsumerProduct::find($consumerProductId);
        // Validate consumer is sms verified with required fields
        $consumer = $consumerProduct->consumer;

        // Only automate qualification for SMS verified leads
        if (!$consumer->first_name ||
            !$consumer->last_name ||
            !$consumer->email ||
            !$consumer->formatted_phone ||
            ($consumer->classification !== Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_SMS) ||
            $consumerProduct->own_property !== 'yes' )
        {
            return false;
        }

        // Get industry service for lead
        $industryService = $consumerProduct->industryService;

        // Get QA automation configurations for industry service
        $qaConfigs = QAAutomationIndustryService::query()
            ->where(QAAutomationIndustryService::FIELD_INDUSTRY_SERVICE_ID, $industryService->{IndustryService::FIELD_ID})
            ->where(QAAutomationIndustryService::FIELD_ENABLED, true)
            ->get();

        // If automation is not enabled for industry service return
        if ($qaConfigs->isEmpty()){
            return false;
        }

        // Run each QA Automation Configuration
        foreach ($qaConfigs as $quConfig) {
            // Regex check
            if ($quConfig->{QAAutomationIndustryService::FIELD_TYPE} === QAAutomationIndustryService::TYPE_REGEX) {
                // If Failed regex check, return - log created in regex service
                if (!$this->regexService->qualifyConsumerProduct($consumerProduct))
                    return false;
            }
            // If more types are added for automated QA, add services and call here
        }

        // If all checks pass, return true
        return true;
    }
}
