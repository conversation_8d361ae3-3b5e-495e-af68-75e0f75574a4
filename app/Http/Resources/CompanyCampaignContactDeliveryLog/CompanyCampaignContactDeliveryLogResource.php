<?php

namespace App\Http\Resources\CompanyCampaignContactDeliveryLog;

use App\Campaigns\Delivery\Contacts\Enums\ContactDeliveryLogType;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignContactDeliveryLog;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin CompanyCampaignContactDeliveryLog
 */
class CompanyCampaignContactDeliveryLogResource extends JsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        $campaign = $this->campaign()->withTrashed()->first();
        $company = $campaign?->company ?? null;

        return [
            'id'                => $this->id,
            'date'              => $this->created_at->timestamp,
            'company'           => $company ? [
                                                'id'   => $company->{Company::FIELD_ID} ?? 'N/A',
                                                'name' => $company->{Company::FIELD_NAME} ?? 'N/A',
                                            ] : null,
            'campaign'          => $campaign ? [
                                                'id'     => $campaign->{CompanyCampaign::FIELD_ID} ?? 'N/A',
                                                'name'   => $campaign->{CompanyCampaign::FIELD_NAME} ?? 'N/A',
                                                'status' => $campaign->{CompanyCampaign::FIELD_STATUS}?->getDisplayName() ?? 'N/A',
                                            ] : null,
            'method'            =>  $this->type === ContactDeliveryLogType::SMS ? 'SMS' : 'EMAIL',
            'success'           => $this->success ? 'Success' : 'Failure',
            'payload'           => $this->payload,
        ];
    }

}
