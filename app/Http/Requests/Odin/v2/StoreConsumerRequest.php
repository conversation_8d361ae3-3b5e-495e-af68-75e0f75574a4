<?php

namespace App\Http\Requests\Odin\v2;

use App\Enums\Odin\GlobalConfigurableFields;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductTcpaRecord;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Rules\Phone;
use Illuminate\Validation\Rule;


class StoreConsumerRequest extends BaseOdinRequest
{
    const CONSUMER_KEY       = 'consumer';
    const INDUSTRY           = 'industry';
    const SERVICE            = 'service';
    const PHONE_VERIFIED     = 'phone_verified';
    const APPOINTMENTS       = 'appointments';
    const WATCHDOG_VIDEO_ID  = 'watchdog_video_id';
    const SECONDARY_SERVICES = 'secondary_services';


    /*
     * Determine if the user is authorized to make this request
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request
     * Odin v2 - includes model keys for Address, Consumer and ConsumerProduct
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $create = $this->isMethod('post');

        return [
            self::CONSUMER_KEY . '.' . Consumer::FIELD_EMAIL                         => [Rule::requiredIf($create), 'email'],
            self::CONSUMER_KEY . '.' . Consumer::FIELD_PHONE                         => [new Phone()],
            self::CONSUMER_KEY . '.' . Consumer::FIELD_FIRST_NAME                    => [Rule::requiredIf($create), 'string', 'max:64'],
            self::CONSUMER_KEY . '.' . Consumer::FIELD_LAST_NAME                     => [Rule::requiredIf($create), 'string', 'max:64'],
            self::CONSUMER_KEY . '.' . ConsumerProduct::FIELD_CONTACT_REQUESTS       => 'nullable|integer|min:0|max:4',
            self::CONSUMER_KEY . '.' . Consumer::FIELD_MAX_CONTACT_REQUESTS          => 'nullable|integer|min:0|max:4',
            self::CONSUMER_KEY . '.' . Address::FIELD_ADDRESS_1                      => [Rule::requiredIf($create), 'string', 'max:128'],
            self::CONSUMER_KEY . '.' . Address::FIELD_ADDRESS_2                      => ['string', 'max:64', 'nullable'],
            self::CONSUMER_KEY . '.' . Address::FIELD_CITY                           => [Rule::requiredIf($create), 'string', 'max:64'],
            self::CONSUMER_KEY . '.' . Address::FIELD_STATE                          => [Rule::requiredIf($create), 'string', 'max:2'],
            self::CONSUMER_KEY . '.' . Address::FIELD_ZIP_CODE                       => [Rule::requiredIf($create), 'string', 'max:5'],
            self::CONSUMER_KEY . '.' . Address::FIELD_COUNTRY                        => 'nullable|string|max:2',
            self::INDUSTRY                                                           => ['nullable', 'exists:' . Industry::TABLE . ',' . Industry::FIELD_SLUG],
            self::SERVICE                                                            => [Rule::requiredIf($create), 'exists:' . IndustryService::TABLE . ',' . IndustryService::FIELD_SLUG],
            self::CONSUMER_KEY . '.' . self::INDUSTRY                                => ['nullable', 'exists:' . Industry::TABLE . ',' . Industry::FIELD_SLUG],
            self::CONSUMER_KEY . '.' . self::SERVICE                                 => ['nullable', 'exists:' . IndustryService::TABLE . ',' . IndustryService::FIELD_SLUG],
            self::CONSUMER_KEY . '.' . self::PHONE_VERIFIED                          => ['boolean'],
            ConsumerProductTcpaRecord::FIELD_TCPA_ID                                 => 'uuid|nullable', // Change to Rule::requiredIf($create) when Watchdog ready
            self::CONSUMER_KEY . '.' . self::APPOINTMENTS                            => ['array'],
            self::CONSUMER_KEY . '.' . GlobalConfigurableFields::TCPA_OPT_INS->value => ['nullable', 'array'],
            self::WATCHDOG_VIDEO_ID                                                  => ['nullable', 'string'],
            self::SECONDARY_SERVICES                                                 => ['nullable', 'array'],
            self::SECONDARY_SERVICES  .'.*'                                          => ['string'],
        ];
    }

    /**
     * @return void
     */
    public function passedValidation(): void
    {
        $consumer = $this->request->all()[self::CONSUMER_KEY] ?? [];
        $maxRequests = $consumer[Consumer::FIELD_MAX_CONTACT_REQUESTS] ?? $consumer[ConsumerProduct::FIELD_CONTACT_REQUESTS] ?? null;

        $consumer[Address::FIELD_COUNTRY] = $this->{Address::FIELD_COUNTRY} ?? 'US';
        $consumer[Consumer::FIELD_PHONE] = Phone::preparePhoneNumberForValidation($this->{self::CONSUMER_KEY .'.'. Consumer::FIELD_PHONE} ?? "");
        if (!is_null($maxRequests)) {
            $consumer[Consumer::FIELD_MAX_CONTACT_REQUESTS] = $maxRequests;
        }

        $this->merge([self::CONSUMER_KEY => $consumer]);
    }
}
