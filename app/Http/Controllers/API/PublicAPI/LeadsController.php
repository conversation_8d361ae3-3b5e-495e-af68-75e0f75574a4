<?php

namespace App\Http\Controllers\API\PublicAPI;

use App\Http\Controllers\API\APIController;
use App\Http\Requests\API\CloneLeadRequest;
use App\Models\Odin\ConsumerProduct;
use App\Services\API\LeadCloneService;
use Illuminate\Http\JsonResponse;

class LeadsController extends APIController
{
    const string RESPONSE_ERROR = 'error';
    const string RESPONSE_CLONED_CONSUMER_PRODUCT_ID = 'cloned_consumer_product_id';

    public function clone(CloneLeadRequest $request, LeadCloneService $service): JsonResponse
    {
        $consumerProduct = ConsumerProduct::query()->findOrFail($request->validated(CloneLeadRequest::CONSUMER_PRODUCT_ID));

        if ($consumerProduct->cloned_from_id || $consumerProduct->clones()->exists()) {
            return $this->formatResponse([
                self::RESPONSE_ERROR => 'Cloning failed. The specified consumer product is not eligible for cloning.'
            ], 400);
        }

        return $this->formatResponse([
            self::RESPONSE_CLONED_CONSUMER_PRODUCT_ID => $service->cloneLead($consumerProduct)->id
        ]);
    }
}
