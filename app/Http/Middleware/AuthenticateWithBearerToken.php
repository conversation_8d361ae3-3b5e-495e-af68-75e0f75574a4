<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Validation\UnauthorizedException;
use Symfony\Component\HttpFoundation\Response;

class AuthenticateWithBearerToken
{
    /**
     * Handle an incoming request.
     *
     * @param Closure(Request): (Response) $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!$this->canAuthenticate($request)) {
            throw new UnauthorizedException();
        }

        return $next($request);
    }

    public function canAuthenticate(Request $request): bool
    {
        // Token validation is currently based on the global config.
        // TODO: Investigate alternative token management strategies.

        return false;
    }
}
