<?php

namespace App\Http\Middleware;

use App\Enums\GlobalConfigurationKey;
use App\Services\GlobalConfigurationService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Validation\UnauthorizedException;
use Symfony\Component\HttpFoundation\Response;

class AuthenticateWithBearerToken
{
    public function __construct(protected GlobalConfigurationService $globalConfigurationService)
    {
    }

    /**
     * Handle an incoming request.
     *
     * @param Closure(Request): (Response) $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!$this->canAuthenticate($request)) {
            throw new UnauthorizedException();
        }

        return $next($request);
    }

    public function canAuthenticate(Request $request): bool
    {
        $bearerToken = $request->bearerToken();

        if (empty($bearerToken)) {
            return false;
        }
        app(GlobalConfigurationRepository::class)->getConfigurationPayload(GlobalConfigurationKey::LEAD_REQUEST_AUTHORIZATION_TOKENS);
        $authorizedTokensConfig = $this->globalConfigurationService->getConfigData(
            GlobalConfigurationKey::LEAD_REQUEST_AUTHORIZATION_TOKENS
        );

        $authorizedTokens = $authorizedTokensConfig['tokens'] ?? [];
        return in_array($bearerToken, $authorizedTokens, true);
    }
}
