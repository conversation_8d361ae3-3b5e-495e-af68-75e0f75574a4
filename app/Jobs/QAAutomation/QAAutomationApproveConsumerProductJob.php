<?php

namespace App\Jobs\QAAutomation;

use App\Models\LeadProcessor;
use App\Models\Odin\ConsumerProduct;
use App\Models\QAAutomation\QAAutomationLog;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Services\Odin\ProductProcessing\ProductProcessingService;
use App\Services\QueueHelperService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;
use Throwable;

class QAAutomationApproveConsumerProductJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    /**
     * @param int $consumerProductId
     */
    public function __construct(
        protected int $consumerProductId,
    )
    {
        $this->queue = QueueHelperService::QUEUE_NAME_LEAD_ALLOCATION_QUEUE;
    }

    /**
     * @param ConsumerProductRepository $consumerProductRepository
     * @param ProductProcessingService $productProcessingService
     * @return void
     * @throws BindingResolutionException
     * @throws Throwable
     */
    public function handle(
        ConsumerProductRepository $consumerProductRepository,
        ProductProcessingService $productProcessingService,
    ): void
    {
        $consumerProduct = $consumerProductRepository->findOrFail($this->consumerProductId);

        //reserve this product to the system user
        $productProcessingService->reserveProduct($consumerProduct, LeadProcessor::systemProcessor());

        $consumerProductRepository->updateConsumerProductStatus($consumerProduct, ConsumerProduct::STATUS_PENDING_ALLOCATION);

        $productProcessingService->approveProduct(
            consumerProduct: $consumerProduct,
            processor: LeadProcessor::systemProcessor(),
            reason: "Automated QA",
        );

        $consumerProductRepository->markConsumerProductGoodToSell($consumerProduct);

        QAAutomationLog::create([
            QAAutomationLog::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id,
            QAAutomationLog::FIELD_ENTRY => 'Consumer Product Passed QA',
            QAAutomationLog::FIELD_LOG_LEVEL => QAAutomationLog::LEVEL_APPROVE,
        ]);
    }

    /**
     * @param Throwable|null $exception
     * @return void
     * @throws Throwable
     */
    public function failed(?Throwable $exception): void
    {
        QAAutomationLog::create([
            QAAutomationLog::FIELD_CONSUMER_PRODUCT_ID => $this->consumerProductId,
            QAAutomationLog::FIELD_LOG_LEVEL => QAAutomationLog::LEVEL_ERROR,
            QAAutomationLog::FIELD_ENTRY => "QA Automation Allocation Job Failure. Error: " . $exception?->getMessage() ?? '',
        ]);
        throw $exception;
    }
}
