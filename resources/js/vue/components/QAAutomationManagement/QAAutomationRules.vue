<template>
    <simple-table
        :data="rules"
        :dark-mode="darkMode"
        :headers="headers"
        title="QA Automation Rules"
        no-pagination
        :loading="loading"
        row-classes="gap-5 grid items-center py-4 rounded px-5"
    >
        <template #title-actions>
            <custom-button @click="openCreateModal">
                Add Rule
            </custom-button>
        </template>
        <template #row.col.type="{type}">
            <div class="">
                <p v-if="type=1">Regex</p>
                <p v-else>Other</p>
            </div>
        </template>
        <template #row.col.match_success="{item}">
            <div class="">
                <p v-if="item.match_success">True</p>
                <p v-else>False</p>
            </div>
        </template>
        <template #row.col.enabled="{item}">
            <div class="">
                <badge v-if="item.enabled" color="green">True</badge>
                <badge v-else color="red">False</badge>
            </div>
        </template>
        <template #row.col.updated_at="{value}">
            <p class="text-sm">
                {{ $filters.dateFromTimestamp(value, 'usWithTime') }}
            </p>
        </template>
        <template #row.col.actions="{item}">
            <div class="flex items-center gap-1">
                <simple-icon
                    :icon="simpleIcon.icons.COG_SIX_TOOTH"
                    @click="handleEdit(item)"
                    clickable
                    :color="simpleIcon.colors.GRAY"
                />
                <simple-icon
                    :icon="simpleIcon.icons.BIN"
                    @click="handleDelete(item)"
                    clickable
                    :color="simpleIcon.colors.RED"
                />
            </div>
        </template>
    </simple-table>
    <modal v-if="showCreateOrUpdateModal" :dark-mode="darkMode" confirm-text="Save"
           @confirm="createOrUpdateRule" :small=true @close="closeModal">
        <template v-slot:header>
            <p v-if="!itemId" class="font-semibold">Add New QA Automation Rule</p>
            <p v-else class="font-semibold">Update QA Automation Rule {{ itemId }}</p>
        </template>
        <template v-slot:content class="flex items-center justify-center">
            <div class="grid grid-cols-3">
                <div>
                    <p>Type</p>
                    <dropdown class="max-w-32" :options="typeOptions" :dark-mode="darkMode" :placeholder="'Select Type'" v-model="itemType"></dropdown>
                </div>
                <div>
                    <p>Enabled</p>
                    <toggle-switch v-model="itemEnabled" :dark-mode="darkMode"></toggle-switch>
                </div>
                <div>
                    <p>Match Success</p>
                    <toggle-switch v-model="itemMatchSuccess" :dark-mode="darkMode"></toggle-switch>
                </div>
            </div>
            <div class="my-5 w-full">
                Expression
                <textarea
                    class="min-h-88 w-full border rounded pl-4 focus:outline-none focus:border focus:border-primary-500 pr-4"
                    placeholder="Enter Expression" type="text" v-model="itemExpression"
                    :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}"/>
            </div>
            <p>Fields</p>
            <multi-select class="max-w-40" :options="fieldOptions" :dark-mode="darkMode" :selected-ids="itemFields" :showSearchBox="false"></multi-select>
        </template>
    </modal>

    <modal v-if="showDeleteModal" :dark-mode="darkMode" confirm-text="Delete"
           @confirm="deleteRule" :small=true @close="closeModal">
        <template v-slot:header>
            <p class="font-semibold">Delete QA Automation Rule {{itemId}}</p>
        </template>
        <template v-slot:content class="flex items-center justify-center">
            Are you sure you want to delete this rule?<br><br>
            {{itemExpression}}
        </template>
    </modal>
</template>

<script>
import {defineComponent} from "vue";
import ToggleSwitch from "../Shared/components/ToggleSwitch.vue";
import Dropdown from "../Shared/components/Dropdown.vue";
import CustomButton from "../Shared/components/CustomButton.vue";
import {PERMISSIONS, useRolesPermissions} from "../../../stores/roles-permissions.store.js";
import SimpleTable from "../Shared/components/SimpleTable/SimpleTable.vue";
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../composables/useSimpleIcon.js";
import Api from "./Services/api.js"
import Modal from "../Shared/components/Modal.vue";
import DropdownSelector from "../Shared/components/DropdownSelector.vue";
import MultiSelect from "../Shared/components/MultiSelect.vue";
import {useToastNotificationStore} from "../../../stores/billing/tost-notification.store.js";
import { expression } from "@babel/template";
import Badge from "../Shared/components/Badge.vue";

export default defineComponent({
    name: "QAAutomationRules",
    components: { MultiSelect, DropdownSelector, Modal, SimpleIcon, SimpleTable, ToggleSwitch, Dropdown, CustomButton, Badge},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            Api,
            simpleIcon: useSimpleIcon(),
            permissionStore: useRolesPermissions(),
            loading: false,
            headers: [
                {title: "ID", field: "id"},
                {title: "Type", field: "type"},
                {title: "Enabled", field: "enabled"},
                {title: "Match Success", field: "match_success"},
                {title: "Expression", field: "expression", cols: 5},
                {title: "Fields", field: "fields", sortable: true, cols: 3},
                {title: "Updated At", field: "updated_at"},
                {title: "Actions", field: "actions"},
            ],
            rules: [],
            showCreateOrUpdateModal: false,
            showEditModal: false,
            showDeleteModal: false,

            typeOptions: [
                {name: 'Regex', id: 1}
            ],

            fieldOptions: [
                {name: 'First Name', id: 'first_name'},
                {name: 'Last Name', id: 'last_name'},
                {name: 'Full Name', id: 'full_name'},
                {name: 'Email', id: 'email'},
                {name: 'Phone', id: 'formatted_phone'},
            ],

            item: null,

            itemId: null,
            itemType: 1,
            itemEnabled: true,
            itemMatchSuccess: false,
            itemExpression: null,
            itemFields: [],

            toastNotificationStore: useToastNotificationStore(),
        }
    },
    created() {
        this.getRules();
    },
    methods: {
        expression,
        openCreateModal() {
            this.showCreateOrUpdateModal = true;
        },
        createOrUpdateRule() {
            if (this.itemId) {
                this.updateRule();
            } else {
                this.createRule();
            }
        },
        createRule() {
            this.Api.createRule({
                type: this.itemType,
                enabled: this.itemEnabled,
                match_success: this.itemMatchSuccess,
                expression: this.itemExpression,
                fields: this.itemFields,
            }).then(resp => {
                this.getRules();
                this.closeModal();
            }).catch(err => this.toastNotificationStore.notifyError(err.message));
        },
        updateRule() {
            this.Api.updateRule(this.itemId, {
                type: this.itemType,
                enabled: this.itemEnabled,
                match_success: this.itemMatchSuccess,
                expression: this.itemExpression,
                fields: this.itemFields,
            }).then(resp => {
                this.getRules();
                this.closeModal();
            }).catch(err => this.toastNotificationStore.notifyError(err.message));
        },
        handleEdit(item) {
            this.itemId = item.id;
            this.itemType = item.type;
            this.itemEnabled = item.enabled;
            this.itemMatchSuccess = item.match_success;
            this.itemExpression = item.expression;
            this.itemFields = item.fields;

            this.showCreateOrUpdateModal = true;
        },
        handleDelete(item) {
            this.itemId = item.id;
            this.itemType = item.type;
            this.itemEnabled = item.enabled;
            this.itemMatchSuccess = item.match_success;
            this.itemExpression = item.expression;
            this.itemFields = item.fields;
            this.showDeleteModal = true;
        },
        deleteRule() {
            this.Api.deleteRule(this.itemId).then(resp => {
                this.getRules();
                this.closeModal();
            }).catch(err => this.toastNotificationStore.notifyError(err.message));
        },
        closeModal() {
            this.showCreateOrUpdateModal = false;
            this.showDeleteModal = false;

            this.itemId = null;
            this.itemType = 1;
            this.itemEnabled = true;
            this.itemMatchSuccess = false;
            this.itemExpression = null;
            this.itemFields = [];
        },
        getRules() {
            this.loading = true;
            this.Api.getRules().then(resp => {
                this.rules = resp.data.data.rules;
                this.loading = false;
            }).catch(err => {
                this.$emit('error-message', err.message);
                console.log(err.message);
                this.loading = false;
            });
        }
    },
    computed: {
        canEdit() {
            return this.permissionStore.hasPermission(PERMISSIONS.QA_AUTOMATION_INDUSTRY_SERVICE_MANAGEMENT_EDIT);
        },
        canDelete() {
            return this.permissionStore.hasPermission(PERMISSIONS.QA_AUTOMATION_INDUSTRY_SERVICE_MANAGEMENT_DELETE);
        }
    }
})
</script>
