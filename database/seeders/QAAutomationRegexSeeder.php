<?php

namespace Database\Seeders;

use App\Enums\Advertising\Advertiser;
use App\Enums\Advertising\AdvertisingPlatform;
use App\Enums\Odin\Industry;
use App\Models\DailyAdCostAccount;
use App\Models\Odin\IndustryService;
use App\Models\QAAutomation\QAAutomationRule;
use Illuminate\Database\Seeder;

class QAAutomationRegexSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        QAAutomationRule::updateOrCreate(
            [
                QAAutomationRule::FIELD_EXPRESSION => '/test/i',
                QAAutomationRule::FIELD_TYPE => QAAutomationRule::TYPE_REGEX,
            ],
            [
                QAAutomationRule::FIELD_FIELDS => ['first_name','last_name','email'],
                QAAutomationRule::FIELD_MATCH_SUCCESS => false,
                QAAutomationRule::FIELD_ENABLED => true,
            ]
        );

        QAAutomationRule::updateOrCreate(
            [
                QAAutomationRule::FIELD_EXPRESSION => '/example/i',
                QAAutomationRule::FIELD_TYPE => QAAutomationRule::TYPE_REGEX,
            ],
            [
                QAAutomationRule::FIELD_FIELDS => ['first_name','last_name','email'],
                QAAutomationRule::FIELD_MATCH_SUCCESS => false,
                QAAutomationRule::FIELD_ENABLED => true,
            ]
        );

        QAAutomationRule::updateOrCreate(
            [
                QAAutomationRule::FIELD_EXPRESSION => '/fuck/i',
                QAAutomationRule::FIELD_TYPE => QAAutomationRule::TYPE_REGEX,
            ],
            [
                QAAutomationRule::FIELD_FIELDS => ['first_name','last_name','email'],
                QAAutomationRule::FIELD_MATCH_SUCCESS => false,
                QAAutomationRule::FIELD_ENABLED => true,
            ]
        );

        QAAutomationRule::updateOrCreate(
            [
                QAAutomationRule::FIELD_EXPRESSION => '/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/i',
                QAAutomationRule::FIELD_TYPE => QAAutomationRule::TYPE_REGEX,
            ],
            [
                QAAutomationRule::FIELD_FIELDS => ['email'],
                QAAutomationRule::FIELD_MATCH_SUCCESS => true,
                QAAutomationRule::FIELD_ENABLED => true,
            ]
        );

        QAAutomationRule::updateOrCreate(
            [
                QAAutomationRule::FIELD_EXPRESSION => '/noway/i',
                QAAutomationRule::FIELD_TYPE => QAAutomationRule::TYPE_REGEX,
            ],
            [
                QAAutomationRule::FIELD_FIELDS => ['first_name','last_name','email'],
                QAAutomationRule::FIELD_MATCH_SUCCESS => false,
                QAAutomationRule::FIELD_ENABLED => true,
            ]
        );

        QAAutomationRule::updateOrCreate(
            [
                QAAutomationRule::FIELD_EXPRESSION => '/donald trump/i',
                QAAutomationRule::FIELD_TYPE => QAAutomationRule::TYPE_REGEX,
            ],
            [
                QAAutomationRule::FIELD_FIELDS => ['full_name'],
                QAAutomationRule::FIELD_MATCH_SUCCESS => false,
                QAAutomationRule::FIELD_ENABLED => true,
            ]
        );

        QAAutomationRule::updateOrCreate(
            [
                QAAutomationRule::FIELD_EXPRESSION => '/nah/i',
                QAAutomationRule::FIELD_TYPE => QAAutomationRule::TYPE_REGEX,
            ],
            [
                QAAutomationRule::FIELD_FIELDS => ['full_name', 'email'],
                QAAutomationRule::FIELD_MATCH_SUCCESS => false,
                QAAutomationRule::FIELD_ENABLED => true,
            ]
        );

        QAAutomationRule::updateOrCreate(
            [
                QAAutomationRule::FIELD_EXPRESSION => '/nope/i',
                QAAutomationRule::FIELD_TYPE => QAAutomationRule::TYPE_REGEX,
            ],
            [
                QAAutomationRule::FIELD_FIELDS => ['full_name', 'email'],
                QAAutomationRule::FIELD_MATCH_SUCCESS => false,
                QAAutomationRule::FIELD_ENABLED => true,
            ]
        );

        QAAutomationRule::updateOrCreate(
            [
                QAAutomationRule::FIELD_EXPRESSION => '/no.thanks@/i',
                QAAutomationRule::FIELD_TYPE => QAAutomationRule::TYPE_REGEX,
            ],
            [
                QAAutomationRule::FIELD_FIELDS => ['email'],
                QAAutomationRule::FIELD_MATCH_SUCCESS => false,
                QAAutomationRule::FIELD_ENABLED => true,
            ]
        );

        QAAutomationRule::updateOrCreate(
            [
                QAAutomationRule::FIELD_EXPRESSION => '/none/i',
                QAAutomationRule::FIELD_TYPE => QAAutomationRule::TYPE_REGEX,
            ],
            [
                QAAutomationRule::FIELD_FIELDS => ['full_name', 'email'],
                QAAutomationRule::FIELD_MATCH_SUCCESS => false,
                QAAutomationRule::FIELD_ENABLED => true,
            ]
        );

        QAAutomationRule::updateOrCreate(
            [
                QAAutomationRule::FIELD_EXPRESSION => '/^.{3}$/', // First and last name both one letter
                QAAutomationRule::FIELD_TYPE => QAAutomationRule::TYPE_REGEX,
            ],
            [
                QAAutomationRule::FIELD_FIELDS => ['full_name'],
                QAAutomationRule::FIELD_MATCH_SUCCESS => false,
                QAAutomationRule::FIELD_ENABLED => true,
            ]
        );

        QAAutomationRule::updateOrCreate(
            [
                QAAutomationRule::FIELD_EXPRESSION => '/^\d{3}555\d{4}$/', // 555 numbers
                QAAutomationRule::FIELD_TYPE => QAAutomationRule::TYPE_REGEX,
            ],
            [
                QAAutomationRule::FIELD_FIELDS => ['formatted_phone'],
                QAAutomationRule::FIELD_MATCH_SUCCESS => false,
                QAAutomationRule::FIELD_ENABLED => true,
            ]
        );

        QAAutomationRule::updateOrCreate(
            [
                QAAutomationRule::FIELD_EXPRESSION => '/^([A-Za-z])\1+$/', // Matches a name composed of the same letter repeated (e.g. "AAAA", "bbb").
                QAAutomationRule::FIELD_TYPE => QAAutomationRule::TYPE_REGEX,
            ],
            [
                QAAutomationRule::FIELD_FIELDS => ['full_name'],
                QAAutomationRule::FIELD_MATCH_SUCCESS => false,
                QAAutomationRule::FIELD_ENABLED => true,
            ]
        );

        QAAutomationRule::updateOrCreate(
            [
                QAAutomationRule::FIELD_EXPRESSION => '/^(.+)\s+\1$/', // Catches a double-word name where both parts are identical, like "abc abc"
                QAAutomationRule::FIELD_TYPE => QAAutomationRule::TYPE_REGEX,
            ],
            [
                QAAutomationRule::FIELD_FIELDS => ['full_name'],
                QAAutomationRule::FIELD_MATCH_SUCCESS => false,
                QAAutomationRule::FIELD_ENABLED => true,
            ]
        );

        QAAutomationRule::updateOrCreate(
            [
                QAAutomationRule::FIELD_EXPRESSION => '/(?i)\b(test|dummy|fake|name|firstname|lastname|fname|lname|unknown|none|n\/?a|asdf|qwerty|john doe|jane doe)\b/',
                QAAutomationRule::FIELD_TYPE => QAAutomationRule::TYPE_REGEX,
            ],
            [
                QAAutomationRule::FIELD_FIELDS => ['full_name', 'email'],
                QAAutomationRule::FIELD_MATCH_SUCCESS => false,
                QAAutomationRule::FIELD_ENABLED => true,
            ]
        );

        QAAutomationRule::updateOrCreate(
            [
                QAAutomationRule::FIELD_EXPRESSION => '/[0-9]/', // No numbers in name
                QAAutomationRule::FIELD_TYPE => QAAutomationRule::TYPE_REGEX,
            ],
            [
                QAAutomationRule::FIELD_FIELDS => ['full_name'],
                QAAutomationRule::FIELD_MATCH_SUCCESS => false,
                QAAutomationRule::FIELD_ENABLED => true,
            ]
        );

        QAAutomationRule::updateOrCreate(
            [
                QAAutomationRule::FIELD_EXPRESSION => "/[^A-Za-z' \-]/", //  Detects any character outside of letters, space, apostrophe, or hyphen
                QAAutomationRule::FIELD_TYPE => QAAutomationRule::TYPE_REGEX,
            ],
            [
                QAAutomationRule::FIELD_FIELDS => [''],
                QAAutomationRule::FIELD_MATCH_SUCCESS => false,
                QAAutomationRule::FIELD_ENABLED => true,
            ]
        );

        QAAutomationRule::updateOrCreate(
            [
                QAAutomationRule::FIELD_EXPRESSION => "/^[A-Za-z' \-]+$/",
                QAAutomationRule::FIELD_TYPE => QAAutomationRule::TYPE_REGEX,
            ],
            [
                QAAutomationRule::FIELD_FIELDS => ['full_name'],
                QAAutomationRule::FIELD_MATCH_SUCCESS => true,
                QAAutomationRule::FIELD_ENABLED => true,
            ]
        );

        QAAutomationRule::updateOrCreate(
            [
                QAAutomationRule::FIELD_EXPRESSION => '/^(?:1234567890|(\d)\1{9})$/',
                QAAutomationRule::FIELD_TYPE => QAAutomationRule::TYPE_REGEX,
            ],
            [
                QAAutomationRule::FIELD_FIELDS => ['formatted_phone'],
                QAAutomationRule::FIELD_MATCH_SUCCESS => false,
                QAAutomationRule::FIELD_ENABLED => true,
            ]
        );

        QAAutomationRule::updateOrCreate(
            [
                QAAutomationRule::FIELD_EXPRESSION => "/(?i)^(none|n\/?a|null|no\s*email)$/",
                QAAutomationRule::FIELD_TYPE => QAAutomationRule::TYPE_REGEX,
            ],
            [
                QAAutomationRule::FIELD_FIELDS => ['email'],
                QAAutomationRule::FIELD_MATCH_SUCCESS => false,
                QAAutomationRule::FIELD_ENABLED => true,
            ]
        );

        QAAutomationRule::updateOrCreate(
            [
                QAAutomationRule::FIELD_EXPRESSION => "/@(?:mailinator\.com|yopmail\.com|guerrillamail\.com|10minutemail\.)/",
                QAAutomationRule::FIELD_TYPE => QAAutomationRule::TYPE_REGEX, // disposable email domains
            ],
            [
                QAAutomationRule::FIELD_FIELDS => ['email'],
                QAAutomationRule::FIELD_MATCH_SUCCESS => false,
                QAAutomationRule::FIELD_ENABLED => true,
            ]
        );

        QAAutomationRule::updateOrCreate(
            [
                QAAutomationRule::FIELD_EXPRESSION => "/^(?i)(test|fake|temp|dummy)\w*@/",
                QAAutomationRule::FIELD_TYPE => QAAutomationRule::TYPE_REGEX,
            ],
            [
                QAAutomationRule::FIELD_FIELDS => ['email'],
                QAAutomationRule::FIELD_MATCH_SUCCESS => false,
                QAAutomationRule::FIELD_ENABLED => true,
            ]
        );

        QAAutomationRule::updateOrCreate(
            [
                QAAutomationRule::FIELD_EXPRESSION => "/^[^@]+@[^@]+$/", // only one @
                QAAutomationRule::FIELD_TYPE => QAAutomationRule::TYPE_REGEX,
            ],
            [
                QAAutomationRule::FIELD_FIELDS => ['email'],
                QAAutomationRule::FIELD_MATCH_SUCCESS => true,
                QAAutomationRule::FIELD_ENABLED => true,
            ]
        );

        QAAutomationRule::updateOrCreate(
            [
                QAAutomationRule::FIELD_EXPRESSION => "/^(..)\1{4}$/", // Detects a 2-digit sequence repeated 5 times, filling 10 digits total (e.g. "1212121212", "7878787878")
                QAAutomationRule::FIELD_TYPE => QAAutomationRule::TYPE_REGEX,
            ],
            [
                QAAutomationRule::FIELD_FIELDS => ['formatted_phone'],
                QAAutomationRule::FIELD_MATCH_SUCCESS => false,
                QAAutomationRule::FIELD_ENABLED => true,
            ]
        );

        QAAutomationRule::updateOrCreate(
            [
                QAAutomationRule::FIELD_EXPRESSION => "/^(?:0123456789|1234567890|9876543210)$/", // Catches sequential numbers in order (ascending or descending). For example, "0123456789" or "1234567890"
                QAAutomationRule::FIELD_TYPE => QAAutomationRule::TYPE_REGEX,
            ],
            [
                QAAutomationRule::FIELD_FIELDS => ['formatted_phone'],
                QAAutomationRule::FIELD_MATCH_SUCCESS => false,
                QAAutomationRule::FIELD_ENABLED => true,
            ]
        );

        QAAutomationRule::updateOrCreate(
            [
                QAAutomationRule::FIELD_EXPRESSION => "/^(?!(.)\1{6})\d{10}$/", // Ensures the number is 10 digits and is not containing 7 or more of the same digit in a row (this is a stricter check to avoid patterns like “5555555xxx”).
                QAAutomationRule::FIELD_TYPE => QAAutomationRule::TYPE_REGEX,
            ],
            [
                QAAutomationRule::FIELD_FIELDS => ['formatted_phone'],
                QAAutomationRule::FIELD_MATCH_SUCCESS => true,
                QAAutomationRule::FIELD_ENABLED => true,
            ]
        );
    }
}
