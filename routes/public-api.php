<?php

use App\Http\Controllers\API\LeadProcessing\Processing\ProcessingBaseController;
use App\Http\Controllers\API\PublicAPI\LeadsController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Public API Routes
|--------------------------------------------------------------------------
|
| This file contains public, unauthenticated API routes, primarily for health check endpoints
| Do not expose any sensitive information via these endpoints
|
*/

Route::prefix('leads')->group(function () {
    Route::get('api-health-check', [ProcessingBaseController::class, 'checkLastLeadCreationTime']);
});

//todo: middleware
Route::prefix('/ai-leads')->middleware('auth.bearer')->controller(LeadsController::class)->group(function () {
    Route::post('/clone', 'clone');
});
