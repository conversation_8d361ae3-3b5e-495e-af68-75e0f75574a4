<?php

namespace Tests\Feature\Http\Middleware;

use App\DataModels\Odin\ConfigurableFieldDataModel;
use App\Enums\GlobalConfigurationKey;
use App\Http\Middleware\AuthenticateWithBearerToken;
use App\Models\GlobalConfiguration;
use App\Services\GlobalConfigurationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Validation\UnauthorizedException;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class AuthenticateWithBearerTokenTest extends TestCase
{
    use RefreshDatabase;

    private AuthenticateWithBearerToken $middleware;
    private GlobalConfigurationService $globalConfigService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->globalConfigService = $this->app->make(GlobalConfigurationService::class);
        $this->middleware = new AuthenticateWithBearerToken($this->globalConfigService);
    }

    #[Test]
    public function it_allows_request_with_valid_bearer_token(): void
    {
        // Arrange: Create global configuration with authorized tokens
        $authorizedTokens = ['valid-token-123', 'another-valid-token'];
        $this->createGlobalConfiguration($authorizedTokens);

        // Create a request with valid bearer token
        $request = Request::create('/test', 'GET');
        $request->headers->set('Authorization', 'Bearer valid-token-123');

        $nextCalled = false;
        $next = function ($request) use (&$nextCalled) {
            $nextCalled = true;
            return response('success');
        };

        // Act
        $response = $this->middleware->handle($request, $next);

        // Assert
        $this->assertTrue($nextCalled);
        $this->assertEquals('success', $response->getContent());
    }

    #[Test]
    public function it_throws_unauthorized_exception_with_invalid_bearer_token(): void
    {
        // Arrange: Create global configuration with authorized tokens
        $authorizedTokens = ['valid-token-123', 'another-valid-token'];
        $this->createGlobalConfiguration($authorizedTokens);

        // Create a request with invalid bearer token
        $request = Request::create('/test', 'GET');
        $request->headers->set('Authorization', 'Bearer invalid-token');

        $next = function ($request) {
            return response('success');
        };

        // Act & Assert
        $this->expectException(UnauthorizedException::class);
        $this->middleware->handle($request, $next);
    }

    #[Test]
    public function it_throws_unauthorized_exception_with_no_bearer_token(): void
    {
        // Arrange: Create global configuration with authorized tokens
        $authorizedTokens = ['valid-token-123', 'another-valid-token'];
        $this->createGlobalConfiguration($authorizedTokens);

        // Create a request without bearer token
        $request = Request::create('/test', 'GET');

        $next = function ($request) {
            return response('success');
        };

        // Act & Assert
        $this->expectException(UnauthorizedException::class);
        $this->middleware->handle($request, $next);
    }

    #[Test]
    public function it_throws_unauthorized_exception_with_empty_bearer_token(): void
    {
        // Arrange: Create global configuration with authorized tokens
        $authorizedTokens = ['valid-token-123', 'another-valid-token'];
        $this->createGlobalConfiguration($authorizedTokens);

        // Create a request with empty bearer token
        $request = Request::create('/test', 'GET');
        $request->headers->set('Authorization', 'Bearer ');

        $next = function ($request) {
            return response('success');
        };

        // Act & Assert
        $this->expectException(UnauthorizedException::class);
        $this->middleware->handle($request, $next);
    }

    #[Test]
    public function it_throws_unauthorized_exception_when_no_tokens_configured(): void
    {
        // Arrange: Create global configuration with empty tokens array
        $this->createGlobalConfiguration([]);

        // Create a request with bearer token
        $request = Request::create('/test', 'GET');
        $request->headers->set('Authorization', 'Bearer some-token');

        $next = function ($request) {
            return response('success');
        };

        // Act & Assert
        $this->expectException(UnauthorizedException::class);
        $this->middleware->handle($request, $next);
    }

    #[Test]
    public function it_throws_unauthorized_exception_when_configuration_missing(): void
    {
        // Arrange: No global configuration created

        // Create a request with bearer token
        $request = Request::create('/test', 'GET');
        $request->headers->set('Authorization', 'Bearer some-token');

        $next = function ($request) {
            return response('success');
        };

        // Act & Assert
        $this->expectException(UnauthorizedException::class);
        $this->middleware->handle($request, $next);
    }

    #[Test]
    public function can_authenticate_returns_true_for_valid_token(): void
    {
        // Arrange
        $authorizedTokens = ['valid-token-123', 'another-valid-token'];
        $this->createGlobalConfiguration($authorizedTokens);

        $request = Request::create('/test', 'GET');
        $request->headers->set('Authorization', 'Bearer valid-token-123');

        // Act
        $result = $this->middleware->canAuthenticate($request);

        // Assert
        $this->assertTrue($result);
    }

    #[Test]
    public function can_authenticate_returns_false_for_invalid_token(): void
    {
        // Arrange
        $authorizedTokens = ['valid-token-123', 'another-valid-token'];
        $this->createGlobalConfiguration($authorizedTokens);

        $request = Request::create('/test', 'GET');
        $request->headers->set('Authorization', 'Bearer invalid-token');

        // Act
        $result = $this->middleware->canAuthenticate($request);

        // Assert
        $this->assertFalse($result);
    }

    #[Test]
    public function can_authenticate_returns_false_for_missing_token(): void
    {
        // Arrange
        $authorizedTokens = ['valid-token-123', 'another-valid-token'];
        $this->createGlobalConfiguration($authorizedTokens);

        $request = Request::create('/test', 'GET');

        // Act
        $result = $this->middleware->canAuthenticate($request);

        // Assert
        $this->assertFalse($result);
    }

    #[Test]
    public function it_performs_strict_comparison_of_tokens(): void
    {
        // Arrange: Create configuration with numeric token
        $authorizedTokens = ['123'];
        $this->createGlobalConfiguration($authorizedTokens);

        // Create request with integer-like token that should not match string '123'
        $request = Request::create('/test', 'GET');
        $request->headers->set('Authorization', 'Bearer 123');

        // Act
        $result = $this->middleware->canAuthenticate($request);

        // Assert: Should return true because both are strings
        $this->assertTrue($result);
    }

    /**
     * Helper method to create global configuration with authorized tokens
     */
    private function createGlobalConfiguration(array $tokens): void
    {
        GlobalConfiguration::factory()->create([
            GlobalConfiguration::FIELD_CONFIGURATION_KEY => GlobalConfigurationKey::LEAD_REQUEST_AUTHORIZATION_TOKENS->value,
            GlobalConfiguration::FIELD_CONFIGURATION_PAYLOAD => new ConfigurableFieldDataModel(collect([
                'tokens' => $tokens
            ]))
        ]);
    }
}
